import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { MainLayout } from "@/components/layout/main-layout";
import { useAuth } from "@/hooks/use-auth";
import { format } from "date-fns";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

// Types for the report data
interface Campaign {
  id: string;
  name: string;
  startDate: string;
  endDate: string;
  teamId: string;
}

interface MediaItem {
  id: string;
  name: string;
  thumbnail?: string;
}

interface ReportData {
  campaignname: string;
  startdate: string;
  enddate: string;
  totalsites: number;
  totalviews: number;
  thumbnail: string;
  screeninfo: Array<{
    screenname: string;
    screenlocation: string;
    screentotalviews: number;
  }>;
}

interface ScreenPerformanceData {
  screen_name: string;
  screen_location: string;
  start_time: string;
  end_time: string;
  expected_screentime: number;
  average_screentime: number;
  performance: number;
  performance_percentage: number; // Calculated field
}

export default function Reports() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [reportType, setReportType] = useState<"campaign" | "screen">("campaign");
  const [selectedCampaignId, setSelectedCampaignId] = useState<string>("");
  const [selectedMediaId, setSelectedMediaId] = useState<string>("");
  const [startDate, setStartDate] = useState<string>("");
  const [endDate, setEndDate] = useState<string>("");
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [screenPerformanceData, setScreenPerformanceData] = useState<ScreenPerformanceData[] | null>(null);
  const [isGeneratingReport, setIsGeneratingReport] = useState(false);
  const [teamId, setTeamId] = useState<string>("");

  // Get the user's teams
  const { data: teams } = useQuery<any[]>({
    queryKey: [`/api/profiles/${user?.id}/teams`],
    queryFn: async () => {
      if (!user?.id) return [];

      console.log('Fetching teams for user:', user.id);
      const response = await apiRequest('GET', `/api/profiles/${user.id}/teams`);
      const data = await response.json();
      console.log('Teams data received:', data);
      return data;
    },
    enabled: !!user?.id,
  });

  useEffect(() => {
    console.log('Teams data in reports:', teams);
    if (teams && teams.length > 0) {
      console.log('Setting teamId to:', teams[0].id);
      setTeamId(teams[0].id);
    }
  }, [teams]);

  // Fetch campaigns for the team
  const { data: campaigns, isLoading: campaignsLoading, error: campaignsError } = useQuery<Campaign[]>({
    queryKey: [`/api/teams/${teamId}/campaigns`],
    queryFn: async () => {
      if (!teamId) {
        console.log('No teamId, returning empty campaigns array');
        return [];
      }

      console.log('Fetching campaigns for teamId:', teamId);
      const response = await apiRequest('GET', `/api/teams/${teamId}/campaigns`);
      const data = await response.json();
      console.log('Campaigns data received:', data);
      return data;
    },
    enabled: !!teamId,
    staleTime: 0, // Always fetch fresh data
    gcTime: 0, // Don't cache (replaces cacheTime in newer versions)
  });

  // Debug campaigns data
  useEffect(() => {
    console.log('Campaigns data in reports:', campaigns);
    console.log('Campaigns loading:', campaignsLoading);
    console.log('Current teamId:', teamId);
  }, [campaigns, campaignsLoading, teamId]);

  // Fetch media items for selected campaign
  const { data: mediaItems } = useQuery<MediaItem[]>({
    queryKey: [`/api/campaigns/${selectedCampaignId}/media-items`],
    queryFn: async () => {
      if (!selectedCampaignId) return [];

      // Fetch campaign_medias with campaign_type = 0 and join with media_items
      const response = await apiRequest('GET', `/api/campaigns/${selectedCampaignId}/media-items`);
      return response.json();
    },
    enabled: !!selectedCampaignId,
  });

  // Handle campaign selection
  const handleCampaignChange = (campaignId: string) => {
    setSelectedCampaignId(campaignId);
    setSelectedMediaId(""); // Reset media selection

    // Find selected campaign and set dates
    const campaign = campaigns?.find(c => c.id === campaignId);
    if (campaign) {
      setStartDate(campaign.startDate ? format(new Date(campaign.startDate), 'yyyy-MM-dd') : '');
      setEndDate(campaign.endDate ? format(new Date(campaign.endDate), 'yyyy-MM-dd') : '');
    }
  };

  // Generate report function
  const generateReport = async () => {
    if (reportType === "campaign") {
      if (!selectedCampaignId || !startDate || !endDate) {
        toast({
          title: "Missing Information",
          description: "Please select a campaign and date range.",
          variant: "destructive"
        });
        return;
      }
    } else {
      if (!startDate || !endDate) {
        toast({
          title: "Missing Information",
          description: "Please select a date range.",
          variant: "destructive"
        });
        return;
      }
    }

    setIsGeneratingReport(true);
    try {
      if (reportType === "campaign") {
        const response = await apiRequest('POST', '/api/rpc/get_campaign_summary', {
          _campaignid: selectedCampaignId,
          _mediaid: selectedMediaId || null,
          _teamid: teamId,
          _startdate: startDate,
          _enddate: endDate
        });

        const data = await response.json();
        setReportData(data);
        setScreenPerformanceData(null);
      } else {
        const response = await apiRequest('POST', '/api/rpc/get_screen_performance', {
          p_team_id: teamId,
          p_reportstartdate: startDate,
          p_reportenddate: endDate
        });

        const data = await response.json();
        // Calculate performance percentage for each screen
        const dataWithPercentage = data.map((screen: ScreenPerformanceData) => ({
          ...screen,
          performance_percentage: screen.expected_screentime > 0
            ? (screen.average_screentime / screen.expected_screentime) * 100
            : 0
        }));
        setScreenPerformanceData(dataWithPercentage);
        setReportData(null);
      }

      toast({
        title: "Report Generated",
        description: `${reportType === "campaign" ? "Campaign" : "Screen performance"} report has been generated successfully.`
      });
    } catch (error) {
      console.error('Error generating report:', error);
      toast({
        title: "Error",
        description: "Failed to generate report. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsGeneratingReport(false);
    }
  };

  // Export to PDF function
  const exportToPDF = () => {
    if (!reportData && !screenPerformanceData) return;

    const printWindow = window.open('', '_blank');
    if (!printWindow) return;

    let htmlContent: string;
    if (reportType === "campaign" && reportData) {
      htmlContent = generateReportHTML(reportData, user?.team?.name || 'ADLOOPR');
    } else if (reportType === "screen" && screenPerformanceData) {
      htmlContent = generateScreenPerformanceHTML(screenPerformanceData, user?.team?.name || 'ADLOOPR', startDate, endDate);
    } else {
      return;
    }

    printWindow.document.write(htmlContent);
    printWindow.document.close();
    printWindow.print();
  };

  return (
    <MainLayout>
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold">Reports</h2>
          <p className="text-muted-foreground">View and generate reports for your campaigns and screens.</p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Reports</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Report Type Selection */}
          <div className="space-y-2">
            <Label htmlFor="report-type-select">Report Type</Label>
            <Select value={reportType} onValueChange={(value: "campaign" | "screen") => setReportType(value)}>
              <SelectTrigger>
                <SelectValue placeholder="Choose report type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="campaign">Campaign Performance</SelectItem>
                <SelectItem value="screen">Screen Performance</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Campaign Selection - Only show for campaign reports */}
          {reportType === "campaign" && (
            <div className="space-y-2">
              <Label htmlFor="campaign-select">Select Campaign</Label>
              <Select value={selectedCampaignId} onValueChange={handleCampaignChange}>
                <SelectTrigger>
                  <SelectValue placeholder={
                    campaignsLoading ? "Loading campaigns..." :
                    !teamId ? "No team selected" :
                    !campaigns || campaigns.length === 0 ? "No campaigns found" :
                    "Choose a campaign"
                  } />
                </SelectTrigger>
                <SelectContent>
                  {campaigns?.map((campaign) => (
                    <SelectItem key={campaign.id} value={campaign.id}>
                      {campaign.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {/* Debug info */}
              <div className="text-xs text-gray-500">
                Debug: TeamId: {teamId || 'none'}, Campaigns: {campaigns?.length || 0}, Loading: {campaignsLoading ? 'yes' : 'no'}
                {campaignsError && <div className="text-red-500">Error: {campaignsError.message}</div>}
              </div>
            </div>
          )}

          {/* Media Selection - Only show for campaign reports */}
          {reportType === "campaign" && (
            <div className="space-y-2">
              <Label htmlFor="media-select">Select Media File (Optional)</Label>
              <Select value={selectedMediaId} onValueChange={setSelectedMediaId} disabled={!selectedCampaignId}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a media file" />
                </SelectTrigger>
                <SelectContent>
                  {mediaItems?.map((media) => (
                    <SelectItem key={media.id} value={media.id}>
                      {media.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Date Range */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="start-date">Start Date</Label>
              <Input
                id="start-date"
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="end-date">End Date</Label>
              <Input
                id="end-date"
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
              />
            </div>
          </div>

          {/* Generate Report Button */}
          <Button
            onClick={generateReport}
            disabled={isGeneratingReport ||
              (reportType === "campaign" && (!selectedCampaignId || !startDate || !endDate)) ||
              (reportType === "screen" && (!startDate || !endDate))
            }
            className="w-full"
          >
            {isGeneratingReport ? "Generating Report..." : "Generate Report"}
          </Button>
        </CardContent>
      </Card>

      {/* Campaign Report Display */}
      {reportData && reportType === "campaign" && (
        <Card className="mt-6">
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Campaign Report</CardTitle>
            <Button onClick={exportToPDF} variant="outline">
              Export to PDF
            </Button>
          </CardHeader>
          <CardContent>
            <div id="report-content">
              {/* Company Header */}
              <div className="mb-6">
                <h1 className="text-4xl font-bold mb-2">{user?.team?.name || 'ADLOOPR'}</h1>
                <h2 className="text-2xl font-semibold text-gray-700 mb-4">
                  {reportData.campaignname}
                </h2>
              </div>

              {/* Report Info Grid - Dynamic layout based on thumbnail */}
              <div className={`grid gap-8 mb-6 ${reportData.thumbnail ? 'grid-cols-3' : 'grid-cols-2'}`}>
                <div>
                  <h3 className="font-semibold text-lg mb-2">Campaign Dates</h3>
                  <p className="text-gray-600">
                    {format(new Date(reportData.startdate), 'dd-MM-yyyy')} - {format(new Date(reportData.enddate), 'dd-MM-yyyy')}
                  </p>
                </div>
                <div>
                  <h3 className="font-semibold text-lg mb-2">Report Dates</h3>
                  <p className="text-gray-600">
                    {format(new Date(startDate), 'dd-MM-yyyy')} - {format(new Date(endDate), 'dd-MM-yyyy')}
                  </p>
                </div>
                {/* Media Thumbnail - Only show if thumbnail exists */}
                {reportData.thumbnail && (
                  <div className="flex justify-center row-span-2">
                    <img
                      src={reportData.thumbnail}
                      alt="Media thumbnail"
                      className="max-w-full max-h-32 object-contain border rounded"
                    />
                  </div>
                )}

                {/* Second row - No. of Sites and Total Views */}
                <div>
                  <h3 className="font-semibold text-lg mb-2">No. of Sites</h3>
                  <p className="text-gray-600">{reportData.totalsites}</p>
                </div>
                <div>
                  <h3 className="font-semibold text-lg mb-2">Total Views</h3>
                  <p className="text-gray-600">{reportData.totalviews.toLocaleString()}</p>
                </div>
              </div>

              {/* Screen Data Table */}
              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-black text-white">
                      <th className="border border-gray-300 px-4 py-3 text-left">SCREEN NAME</th>
                      <th className="border border-gray-300 px-4 py-3 text-left">LOCATION</th>
                      <th className="border border-gray-300 px-4 py-3 text-right">TOTAL VIEWS</th>
                    </tr>
                  </thead>
                  <tbody>
                    {reportData.screeninfo.map((screen, index) => (
                      <tr key={index} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                        <td className="border border-gray-300 px-4 py-3">{screen.screenname}</td>
                        <td className="border border-gray-300 px-4 py-3">{screen.screenlocation}</td>
                        <td className="border border-gray-300 px-4 py-3 text-right">{screen.screentotalviews.toLocaleString()}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Screen Performance Report Display */}
      {screenPerformanceData && reportType === "screen" && (
        <Card className="mt-6">
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Screen Performance Report</CardTitle>
            <Button onClick={exportToPDF} variant="outline">
              Export to PDF
            </Button>
          </CardHeader>
          <CardContent>
            <div id="screen-report-content">
              {/* Company Header */}
              <div className="mb-6">
                <h1 className="text-4xl font-bold mb-2">{user?.team?.name || 'ADLOOPR'}</h1>
                <h2 className="text-2xl font-semibold text-gray-700 mb-4">
                  Screen Performance Report
                </h2>
              </div>

              {/* Report Info */}
              <div className="grid grid-cols-2 gap-8 mb-6">
                <div>
                  <h3 className="font-semibold text-lg mb-2">Report Period</h3>
                  <p className="text-gray-600">
                    {format(new Date(startDate), 'dd-MM-yyyy')} - {format(new Date(endDate), 'dd-MM-yyyy')}
                  </p>
                </div>
                <div>
                  <h3 className="font-semibold text-lg mb-2">Total Screens</h3>
                  <p className="text-gray-600">{screenPerformanceData.length}</p>
                </div>
              </div>

              {/* Screen Performance Table */}
              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-black text-white">
                      <th className="border border-gray-300 px-4 py-3 text-left">SCREEN NAME</th>
                      <th className="border border-gray-300 px-4 py-3 text-left">LOCATION</th>
                      <th className="border border-gray-300 px-4 py-3 text-center">START TIME</th>
                      <th className="border border-gray-300 px-4 py-3 text-center">END TIME</th>
                      <th className="border border-gray-300 px-4 py-3 text-right">EXPECTED SCREENTIME (HRS)</th>
                      <th className="border border-gray-300 px-4 py-3 text-right">AVERAGE SCREENTIME (HRS)</th>
                      <th className="border border-gray-300 px-4 py-3 text-right">PERFORMANCE</th>
                      <th className="border border-gray-300 px-4 py-3 text-right">PERFORMANCE %</th>
                    </tr>
                  </thead>
                  <tbody>
                    {screenPerformanceData.map((screen, index) => (
                      <tr key={index} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                        <td className="border border-gray-300 px-4 py-3">{screen.screen_name}</td>
                        <td className="border border-gray-300 px-4 py-3">{screen.screen_location}</td>
                        <td className="border border-gray-300 px-4 py-3 text-center">{screen.start_time}</td>
                        <td className="border border-gray-300 px-4 py-3 text-center">{screen.end_time}</td>
                        <td className="border border-gray-300 px-4 py-3 text-right">{screen.expected_screentime}</td>
                        <td className="border border-gray-300 px-4 py-3 text-right">{screen.average_screentime.toFixed(2)}</td>
                        <td className="border border-gray-300 px-4 py-3 text-right">{screen.performance.toFixed(2)}</td>
                        <td className="border border-gray-300 px-4 py-3 text-right">{screen.performance_percentage.toFixed(2)}%</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

    </MainLayout>
  );
}

// Helper function to generate HTML for PDF export
function generateReportHTML(reportData: ReportData, companyName: string): string {
  // Dynamic grid layout based on thumbnail presence
  const gridColumns = reportData.thumbnail ? '1fr 1fr 1fr' : '1fr 1fr';
  const gridRows = reportData.thumbnail ? '2' : '1';
  const thumbnailColumn = reportData.thumbnail ? `
    <div class="info-item thumbnail-item">
      <div style="text-align: center;">
        <img src="${reportData.thumbnail}" alt="Media thumbnail" style="max-width: 200px; max-height: 120px; border: 1px solid #ccc; border-radius: 4px;" />
      </div>
    </div>
  ` : '';

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Campaign Report</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { font-size: 36px; font-weight: bold; margin-bottom: 10px; }
        h2 { font-size: 24px; font-weight: 600; color: #555; margin-bottom: 20px; }
        h3 { font-size: 18px; font-weight: 600; margin-bottom: 10px; }
        .info-grid {
          display: grid;
          grid-template-columns: ${gridColumns};
          grid-template-rows: repeat(${gridRows}, auto);
          gap: 40px;
          margin-bottom: 30px;
        }
        .thumbnail-item {
          grid-row: 1 / span 2;
          grid-column: 3;
        }
        .info-item { margin-bottom: 20px; }
        .info-label { font-weight: 600; font-size: 16px; margin-bottom: 5px; }
        .info-value { color: #666; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #333; padding: 12px; text-align: left; }
        th { background-color: #000; color: white; font-weight: bold; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        tr:nth-child(odd) { background-color: white; }
        .text-right { text-align: right; }
        @media print {
          thead { display: table-header-group; }
          tbody { display: table-row-group; }
          tr { page-break-inside: avoid; }
        }
      </style>
    </head>
    <body>
      <h1>${companyName}</h1>
      <h2>${reportData.campaignname}</h2>

      <div class="info-grid">
        <div class="info-item">
          <div class="info-label">Campaign Dates</div>
          <div class="info-value">${new Date(reportData.startdate).toLocaleDateString('en-GB')} - ${new Date(reportData.enddate).toLocaleDateString('en-GB')}</div>
        </div>
        <div class="info-item">
          <div class="info-label">Report Dates</div>
          <div class="info-value">${new Date(reportData.startdate).toLocaleDateString('en-GB')} - ${new Date(reportData.enddate).toLocaleDateString('en-GB')}</div>
        </div>
        ${thumbnailColumn}
        <div class="info-item">
          <div class="info-label">No. of Sites</div>
          <div class="info-value">${reportData.totalsites}</div>
        </div>
        <div class="info-item">
          <div class="info-label">Total Views</div>
          <div class="info-value">${reportData.totalviews.toLocaleString()}</div>
        </div>
      </div>

      <table>
        <thead>
          <tr>
            <th>SCREEN NAME</th>
            <th>LOCATION</th>
            <th class="text-right">TOTAL VIEWS</th>
          </tr>
        </thead>
        <tbody>
          ${reportData.screeninfo.map(screen => `
            <tr>
              <td>${screen.screenname}</td>
              <td>${screen.screenlocation}</td>
              <td class="text-right">${screen.screentotalviews.toLocaleString()}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
    </body>
    </html>
  `;
}

// Helper function to generate HTML for Screen Performance PDF export
function generateScreenPerformanceHTML(screenData: ScreenPerformanceData[], companyName: string, startDate: string, endDate: string): string {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Screen Performance Report</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { font-size: 36px; font-weight: bold; margin-bottom: 10px; }
        h2 { font-size: 24px; font-weight: 600; color: #555; margin-bottom: 20px; }
        h3 { font-size: 18px; font-weight: 600; margin-bottom: 10px; }
        .info-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 40px;
          margin-bottom: 30px;
        }
        .info-item { margin-bottom: 20px; }
        .info-label { font-weight: 600; font-size: 16px; margin-bottom: 5px; }
        .info-value { color: #666; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #333; padding: 8px; text-align: left; font-size: 12px; }
        th { background-color: #000; color: white; font-weight: bold; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        tr:nth-child(odd) { background-color: white; }
        .text-center { text-align: center; }
        .text-right { text-align: right; }
        @media print {
          thead { display: table-header-group; }
          tbody { display: table-row-group; }
          tr { page-break-inside: avoid; }
        }
      </style>
    </head>
    <body>
      <h1>${companyName}</h1>
      <h2>Screen Performance Report</h2>

      <div class="info-grid">
        <div class="info-item">
          <div class="info-label">Report Period</div>
          <div class="info-value">${new Date(startDate).toLocaleDateString('en-GB')} - ${new Date(endDate).toLocaleDateString('en-GB')}</div>
        </div>
        <div class="info-item">
          <div class="info-label">Total Screens</div>
          <div class="info-value">${screenData.length}</div>
        </div>
      </div>

      <table>
        <thead>
          <tr>
            <th>SCREEN NAME</th>
            <th>LOCATION</th>
            <th class="text-center">START TIME</th>
            <th class="text-center">END TIME</th>
            <th class="text-right">EXPECTED SCREENTIME (HRS)</th>
            <th class="text-right">AVERAGE SCREENTIME (HRS)</th>
            <th class="text-right">PERFORMANCE</th>
            <th class="text-right">PERFORMANCE %</th>
          </tr>
        </thead>
        <tbody>
          ${screenData.map(screen => `
            <tr>
              <td>${screen.screen_name}</td>
              <td>${screen.screen_location}</td>
              <td class="text-center">${screen.start_time}</td>
              <td class="text-center">${screen.end_time}</td>
              <td class="text-right">${screen.expected_screentime}</td>
              <td class="text-right">${screen.average_screentime.toFixed(2)}</td>
              <td class="text-right">${screen.performance.toFixed(2)}</td>
              <td class="text-right">${screen.performance_percentage.toFixed(2)}%</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
    </body>
    </html>
  `;
}
