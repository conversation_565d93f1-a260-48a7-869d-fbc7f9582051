import type { Express, Request, Response } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { z } from "zod";
import axios from "axios";
import {
  insertProfileSchema, insertTeamSchema, insertTeamMemberSchema,
  insertMediaItemSchema, insertScreenSchema, insertCampaignSchema,
  insertCampaignMediaSchema, insertCampaignScreenSchema,
  insertSlideSchema, insertTagSchema, insertInvoiceSchema,
  insertPricingSchema
} from "@shared/schema";

export async function registerRoutes(app: Express): Promise<Server> {
  // Authentication endpoints (these would be handled by Supabase client-side)
  // But we'll add stub routes for backend validation

  // User Profiles
  app.get("/api/profiles/:id", async (req: Request, res: Response) => {
    const profile = await storage.getProfile(req.params.id);
    if (!profile) {
      return res.status(404).json({ message: "Profile not found" });
    }
    res.json(profile);
  });

  app.post("/api/profiles", async (req: Request, res: Response) => {
    try {
      const profileData = insertProfileSchema.parse(req.body);
      const profile = await storage.createProfile(profileData);
      res.status(201).json(profile);
    } catch (error) {
      res.status(400).json({ message: "Invalid profile data", error });
    }
  });

  app.patch("/api/profiles/:id", async (req: Request, res: Response) => {
    try {
      const profileData = insertProfileSchema.partial().parse(req.body);
      const updatedProfile = await storage.updateProfile(req.params.id, profileData);
      if (!updatedProfile) {
        return res.status(404).json({ message: "Profile not found" });
      }
      res.json(updatedProfile);
    } catch (error) {
      res.status(400).json({ message: "Invalid profile data", error });
    }
  });

  // Teams
  app.get("/api/teams/:id", async (req: Request, res: Response) => {
    const team = await storage.getTeam(req.params.id);
    if (!team) {
      return res.status(404).json({ message: "Team not found" });
    }
    res.json(team);
  });

  app.get("/api/profiles/:profileId/teams", async (req: Request, res: Response) => {
    const teams = await storage.getTeamsByProfileId(req.params.profileId);
    res.json(teams);
  });

  app.post("/api/teams", async (req: Request, res: Response) => {
    try {
      const teamData = insertTeamSchema.parse(req.body);
      const team = await storage.createTeam(teamData);
      res.status(201).json(team);
    } catch (error) {
      res.status(400).json({ message: "Invalid team data", error });
    }
  });

  app.patch("/api/teams/:id", async (req: Request, res: Response) => {
    try {
      const teamData = insertTeamSchema.partial().parse(req.body);
      const updatedTeam = await storage.updateTeam(req.params.id, teamData);
      if (!updatedTeam) {
        return res.status(404).json({ message: "Team not found" });
      }
      res.json(updatedTeam);
    } catch (error) {
      res.status(400).json({ message: "Invalid team data", error });
    }
  });

  // Team Members
  app.get("/api/teams/:teamId/members", async (req: Request, res: Response) => {
    const members = await storage.getTeamMembers(req.params.teamId);
    res.json(members);
  });

  app.post("/api/team-members", async (req: Request, res: Response) => {
    try {
      const memberData = insertTeamMemberSchema.parse(req.body);
      const member = await storage.createTeamMember(memberData);
      res.status(201).json(member);
    } catch (error) {
      res.status(400).json({ message: "Invalid team member data", error });
    }
  });

  app.delete("/api/team-members/:teamId/:profileId", async (req: Request, res: Response) => {
    const { teamId, profileId } = req.params;
    const removed = await storage.removeTeamMember(profileId, teamId);
    if (!removed) {
      return res.status(404).json({ message: "Team member not found" });
    }
    res.status(204).end();
  });

  // Media
  app.get("/api/teams/:teamId/media", async (req: Request, res: Response) => {
    const media = await storage.getMediaItemsByTeamId(req.params.teamId);
    res.json(media);
  });

  // Optimized endpoint: Media with tags in a single query
  app.get("/api/teams/:teamId/media-with-tags", async (req: Request, res: Response) => {
    const mediaWithTags = await storage.getMediaItemsWithTagsByTeamId(req.params.teamId);
    res.json(mediaWithTags);
  });

  // Get media by tag ID
  app.get("/api/media", async (req: Request, res: Response) => {
    const tagId = req.query.tagId as string;
    if (!tagId) {
      return res.status(400).json({ message: "tagId query parameter is required" });
    }

    try {
      // Get all media that have this tag
      const mediaWithTag = await storage.getMediaItemsByTagId(tagId);
      res.json(mediaWithTag);
    } catch (error) {
      res.status(500).json({ message: "Error fetching media by tag", error });
    }
  });

  app.get("/api/media/:id", async (req: Request, res: Response) => {
    const media = await storage.getMediaItem(req.params.id);
    if (!media) {
      return res.status(404).json({ message: "Media not found" });
    }
    res.json(media);
  });

  app.post("/api/media", async (req: Request, res: Response) => {
    try {
      const mediaData = insertMediaItemSchema.parse(req.body);
      const media = await storage.createMediaItem(mediaData);
      res.status(201).json(media);
    } catch (error) {
      res.status(400).json({ message: "Invalid media data", error });
    }
  });

  app.patch("/api/media/:id", async (req: Request, res: Response) => {
    try {
      const mediaData = insertMediaItemSchema.partial().parse(req.body);
      const updatedMedia = await storage.updateMediaItem(req.params.id, mediaData);
      if (!updatedMedia) {
        return res.status(404).json({ message: "Media not found" });
      }
      res.json(updatedMedia);
    } catch (error) {
      res.status(400).json({ message: "Invalid media data", error });
    }
  });

  app.delete("/api/media/:id", async (req: Request, res: Response) => {
    const deleted = await storage.deleteMediaItem(req.params.id);
    if (!deleted) {
      return res.status(404).json({ message: "Media not found" });
    }
    res.status(204).end();
  });

  // Screens
  app.get("/api/teams/:teamId/screens", async (req: Request, res: Response) => {
    const screens = await storage.getScreensByTeamId(req.params.teamId);
    res.json(screens);
  });

  // Optimized endpoint: Screens with tags in a single query
  app.get("/api/teams/:teamId/screens-with-tags", async (req: Request, res: Response) => {
    const screensWithTags = await storage.getScreensWithTagsByTeamId(req.params.teamId);
    res.json(screensWithTags);
  });

  // Enhanced endpoint: Screens with tags, activities, and registrations
  app.get("/api/teams/:teamId/enhanced-screens-with-tags", async (req: Request, res: Response) => {
    const enhancedScreensWithTags = await storage.getEnhancedScreensWithTagsByTeamId(req.params.teamId);
    res.json(enhancedScreensWithTags);
  });

  // Get screens by tag ID
  app.get("/api/screens", async (req: Request, res: Response) => {
    const tagId = req.query.tagId as string;
    if (!tagId) {
      return res.status(400).json({ message: "tagId query parameter is required" });
    }

    try {
      // Get all screens that have this tag
      const screensWithTag = await storage.getScreensByTagId(tagId);
      res.json(screensWithTag);
    } catch (error) {
      res.status(500).json({ message: "Error fetching screens by tag", error });
    }
  });

  app.get("/api/screens/:id", async (req: Request, res: Response) => {
    const screen = await storage.getScreen(req.params.id);
    if (!screen) {
      return res.status(404).json({ message: "Screen not found" });
    }
    res.json(screen);
  });

  // Enhanced endpoint: Single screen with activities and registrations
  app.get("/api/screens/:id/enhanced", async (req: Request, res: Response) => {
    const enhancedScreen = await storage.getEnhancedScreen(req.params.id);
    if (!enhancedScreen) {
      return res.status(404).json({ message: "Screen not found" });
    }
    res.json(enhancedScreen);
  });

  app.get("/api/screens/code/:code", async (req: Request, res: Response) => {
    const screen = await storage.getScreenByCode(req.params.code);
    if (!screen) {
      return res.status(404).json({ message: "Screen not found" });
    }
    res.json(screen);
  });

  app.post("/api/screens", async (req: Request, res: Response) => {
    try {
      const screenData = insertScreenSchema.parse(req.body);
      const screen = await storage.createScreen(screenData);
      res.status(201).json(screen);
    } catch (error) {
      res.status(400).json({ message: "Invalid screen data", error });
    }
  });

  app.patch("/api/screens/:id", async (req: Request, res: Response) => {
    try {
      const screenData = insertScreenSchema.partial().parse(req.body);
      const updatedScreen = await storage.updateScreen(req.params.id, screenData);
      if (!updatedScreen) {
        return res.status(404).json({ message: "Screen not found" });
      }
      res.json(updatedScreen);
    } catch (error) {
      res.status(400).json({ message: "Invalid screen data", error });
    }
  });

  app.delete("/api/screens/:id", async (req: Request, res: Response) => {
    const deleted = await storage.deleteScreen(req.params.id);
    if (!deleted) {
      return res.status(404).json({ message: "Screen not found" });
    }
    res.status(204).end();
  });

  // Campaigns
  app.get("/api/teams/:teamId/campaigns", async (req: Request, res: Response) => {
    const campaigns = await storage.getCampaignsByTeamId(req.params.teamId);
    res.json(campaigns);
  });

  app.get("/api/campaigns/:id", async (req: Request, res: Response) => {
    const campaign = await storage.getCampaign(req.params.id);
    if (!campaign) {
      return res.status(404).json({ message: "Campaign not found" });
    }
    res.json(campaign);
  });

  app.post("/api/campaigns", async (req: Request, res: Response) => {
    try {
      const campaignData = insertCampaignSchema.parse(req.body);
      const campaign = await storage.createCampaign(campaignData);
      res.status(201).json(campaign);
    } catch (error) {
      res.status(400).json({ message: "Invalid campaign data", error });
    }
  });

  app.patch("/api/campaigns/:id", async (req: Request, res: Response) => {
    try {
      const campaignData = insertCampaignSchema.partial().parse(req.body);
      const updatedCampaign = await storage.updateCampaign(req.params.id, campaignData);
      if (!updatedCampaign) {
        return res.status(404).json({ message: "Campaign not found" });
      }
      res.json(updatedCampaign);
    } catch (error) {
      res.status(400).json({ message: "Invalid campaign data", error });
    }
  });

  app.delete("/api/campaigns/:id", async (req: Request, res: Response) => {
    const deleted = await storage.deleteCampaign(req.params.id);
    if (!deleted) {
      return res.status(404).json({ message: "Campaign not found" });
    }
    res.status(204).end();
  });

  // Campaign Media
  app.get("/api/campaigns/:campaignId/media", async (req: Request, res: Response) => {
    const media = await storage.getCampaignMedias(req.params.campaignId);
    res.json(media);
  });

  // Campaign Media Items (with media details for reports)
  app.get("/api/campaigns/:campaignId/media-items", async (req: Request, res: Response) => {
    const mediaItems = await storage.getCampaignMediaItems(req.params.campaignId);
    res.json(mediaItems);
  });

  app.post("/api/campaign-media", async (req: Request, res: Response) => {
    try {
      const mediaData = insertCampaignMediaSchema.parse(req.body);
      const media = await storage.createCampaignMedia(mediaData);
      res.status(201).json(media);
    } catch (error) {
      res.status(400).json({ message: "Invalid campaign media data", error });
    }
  });

  app.delete("/api/campaign-media/:campaignId/:mediaId", async (req: Request, res: Response) => {
    const { campaignId, mediaId } = req.params;
    const deleted = await storage.deleteCampaignMedia(campaignId, mediaId);
    if (!deleted) {
      return res.status(404).json({ message: "Campaign media not found" });
    }
    res.status(204).end();
  });

  // Campaign Slides - Using campaign_medias table with campaign_type=1
  app.get("/api/campaigns/:campaignId/slides", async (req: Request, res: Response) => {
    console.log(`Fetching slides for campaign: ${req.params.campaignId}`);
    const slides = await storage.getCampaignSlides(req.params.campaignId);
    console.log(`Found ${slides.length} slides for campaign: ${req.params.campaignId}`);
    res.json(slides);
  });

  app.post("/api/campaign-slides", async (req: Request, res: Response) => {
    try {
      // Log the incoming request for debugging
      console.log("Received campaign-slides POST request with data:", req.body);

      // Redirect to the campaign-media endpoint
      // This is to handle older client code still using the /api/campaign-slides endpoint
      // while we're using a unified table approach in the backend
      const mediaData = {
        campaignId: req.body.campaignId,
        mediaId: req.body.slideId,
        displayOrder: req.body.displayOrder,
        campaignType: 1, // 1 = slide
      };

      console.log("Redirecting to campaign-media with data:", mediaData);
      const result = await storage.createCampaignMedia(mediaData);
      res.status(201).json(result);
    } catch (error) {
      console.error("Error in campaign-slides endpoint:", error);
      res.status(400).json({ message: "Invalid campaign slide data", error });
    }
  });

  app.delete("/api/campaign-slides/:campaignId/:slideId", async (req: Request, res: Response) => {
    try {
      const { campaignId, slideId } = req.params;
      console.log(`Received request to delete slide: campaignId=${campaignId}, slideId=${slideId}`);

      // Use the campaign-media deletion method but specify the slide was deleted
      const deleted = await storage.deleteCampaignMedia(campaignId, slideId);
      if (!deleted) {
        return res.status(404).json({ message: "Campaign slide not found" });
      }
      console.log(`Successfully deleted slide: campaignId=${campaignId}, slideId=${slideId}`);
      res.status(204).end();
    } catch (error) {
      console.error("Error deleting campaign slide:", error);
      res.status(500).json({ message: "Server error deleting campaign slide", error });
    }
  });

  // Campaign Screens
  app.get("/api/campaigns/:campaignId/screens", async (req: Request, res: Response) => {
    const screens = await storage.getCampaignScreens(req.params.campaignId);
    res.json(screens);
  });

  app.get("/api/screens/:screenId/campaigns", async (req: Request, res: Response) => {
    const campaigns = await storage.getScreenCampaigns(req.params.screenId);
    res.json(campaigns);
  });

  app.post("/api/campaign-screens", async (req: Request, res: Response) => {
    try {
      const screenData = insertCampaignScreenSchema.parse(req.body);
      const screen = await storage.createCampaignScreen(screenData);
      res.status(201).json(screen);
    } catch (error) {
      res.status(400).json({ message: "Invalid campaign screen data", error });
    }
  });

  app.delete("/api/campaign-screens/:campaignId/:screenId", async (req: Request, res: Response) => {
    const { campaignId, screenId } = req.params;
    const deleted = await storage.deleteCampaignScreen(campaignId, screenId);
    if (!deleted) {
      return res.status(404).json({ message: "Campaign screen not found" });
    }
    res.status(204).end();
  });

  // Slides
  app.get("/api/teams/:teamId/slides", async (req: Request, res: Response) => {
    const slides = await storage.getSlidesByTeamId(req.params.teamId);
    res.json(slides);
  });

  app.get("/api/slides/:id", async (req: Request, res: Response) => {
    const slide = await storage.getSlide(req.params.id);
    if (!slide) {
      return res.status(404).json({ message: "Slide not found" });
    }
    res.json(slide);
  });

  app.post("/api/slides", async (req: Request, res: Response) => {
    try {
      const slideData = insertSlideSchema.parse(req.body);
      const slide = await storage.createSlide(slideData);
      res.status(201).json(slide);
    } catch (error) {
      res.status(400).json({ message: "Invalid slide data", error });
    }
  });

  app.patch("/api/slides/:id", async (req: Request, res: Response) => {
    try {
      const slideData = insertSlideSchema.partial().parse(req.body);
      const updatedSlide = await storage.updateSlide(req.params.id, slideData);
      if (!updatedSlide) {
        return res.status(404).json({ message: "Slide not found" });
      }
      res.json(updatedSlide);
    } catch (error) {
      res.status(400).json({ message: "Invalid slide data", error });
    }
  });

  app.delete("/api/slides/:id", async (req: Request, res: Response) => {
    const deleted = await storage.deleteSlide(req.params.id);
    if (!deleted) {
      return res.status(404).json({ message: "Slide not found" });
    }
    res.status(204).end();
  });

  // Tags
  app.get("/api/teams/:teamId/tags", async (req: Request, res: Response) => {
    const tags = await storage.getTagsByTeamId(req.params.teamId);
    res.json(tags);
  });

  // Optimized endpoint: Tags with associations in a single query
  app.get("/api/teams/:teamId/tags-with-associations", async (req: Request, res: Response) => {
    const tagsWithAssociations = await storage.getTagsWithAssociationsByTeamId(req.params.teamId);
    res.json(tagsWithAssociations);
  });

  app.get("/api/tags/:id", async (req: Request, res: Response) => {
    const tag = await storage.getTag(req.params.id);
    if (!tag) {
      return res.status(404).json({ message: "Tag not found" });
    }
    res.json(tag);
  });

  app.post("/api/tags", async (req: Request, res: Response) => {
    try {
      const tagData = insertTagSchema.parse(req.body);
      const tag = await storage.createTag(tagData);
      res.status(201).json(tag);
    } catch (error) {
      res.status(400).json({ message: "Invalid tag data", error });
    }
  });

  app.patch("/api/tags/:id", async (req: Request, res: Response) => {
    try {
      const tagData = insertTagSchema.partial().parse(req.body);
      const updatedTag = await storage.updateTag(req.params.id, tagData);
      if (!updatedTag) {
        return res.status(404).json({ message: "Tag not found" });
      }
      res.json(updatedTag);
    } catch (error) {
      res.status(400).json({ message: "Invalid tag data", error });
    }
  });

  app.delete("/api/tags/:id", async (req: Request, res: Response) => {
    const deleted = await storage.deleteTag(req.params.id);
    if (!deleted) {
      return res.status(404).json({ message: "Tag not found" });
    }
    res.status(204).end();
  });

  // Media Tags
  app.get("/api/media/:mediaId/tags", async (req: Request, res: Response) => {
    const tags = await storage.getMediaTags(req.params.mediaId);
    res.json(tags);
  });

  app.post("/api/media/:mediaId/tags/:tagId", async (req: Request, res: Response) => {
    try {
      const mediaId = req.params.mediaId;
      const tagId = req.params.tagId;

      const mediaTag = await storage.addMediaTag(mediaId, tagId);
      res.status(201).json(mediaTag);
    } catch (error) {
      res.status(400).json({ message: "Error adding tag to media", error });
    }
  });

  app.delete("/api/media/:mediaId/tags/:tagId", async (req: Request, res: Response) => {
    const { mediaId, tagId } = req.params;
    const deleted = await storage.removeMediaTag(mediaId, tagId);
    if (!deleted) {
      return res.status(404).json({ message: "Media tag not found" });
    }
    res.status(204).end();
  });

  // Screen Tags
  app.get("/api/screens/:screenId/tags", async (req: Request, res: Response) => {
    const tags = await storage.getScreenTags(req.params.screenId);
    res.json(tags);
  });

  app.post("/api/screens/:screenId/tags/:tagId", async (req: Request, res: Response) => {
    try {
      const screenId = req.params.screenId;
      const tagId = req.params.tagId;

      const screenTag = await storage.addScreenTag(screenId, tagId);
      res.status(201).json(screenTag);
    } catch (error) {
      res.status(400).json({ message: "Error adding tag to screen", error });
    }
  });

  app.delete("/api/screens/:screenId/tags/:tagId", async (req: Request, res: Response) => {
    const { screenId, tagId } = req.params;
    const deleted = await storage.removeScreenTag(screenId, tagId);
    if (!deleted) {
      return res.status(404).json({ message: "Screen tag not found" });
    }
    res.status(204).end();
  });

  // API Proxy to avoid CORS issues
  app.get("/api/proxy", async (req: Request, res: Response) => {
    const url = req.query.url as string;

    if (!url) {
      return res.status(400).json({ message: "URL parameter is required" });
    }

    try {
      console.log(`Proxying request to: ${url}`);
      const response = await axios.get(url, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      });

      res.json(response.data);
    } catch (error) {
      console.error('Proxy error:', error);
      res.status(500).json({
        message: "Error proxying request",
        error: error instanceof Error ? error.message : "Unknown error"
      });
    }
  });

  // Billing endpoints

  // Get pricing options
  app.get("/api/pricing", async (req: Request, res: Response) => {
    const pricing = await storage.getPricing();
    res.json(pricing);
  });

  // Get team billing summary
  app.get("/api/teams/:teamId/billing-summary", async (req: Request, res: Response) => {
    const billingSummary = await storage.getBillingSummary(req.params.teamId);
    res.json(billingSummary);
  });

  // Get team invoices
  app.get("/api/teams/:teamId/invoices", async (req: Request, res: Response) => {
    const invoices = await storage.getInvoicesByTeamId(req.params.teamId);
    res.json(invoices);
  });

  // Get campaign log summary for content engagement chart (old endpoint - keep for backward compatibility)
  app.get("/api/teams/:teamId/campaign-log-summary", async (req: Request, res: Response) => {
    console.log('⚠️ OLD ENDPOINT CALLED: /campaign-log-summary');
    console.log('Request params:', req.params);
    console.log('Request query:', req.query);

    const timeRange = req.query.timeRange as 'week' | 'month' | 'year' || 'week';
    const campaignLogSummary = await storage.getCampaignLogSummary(req.params.teamId, timeRange);
    res.json(campaignLogSummary);
  });

  // Get campaign summary timeseries for content engagement chart (new RPC-based endpoint)
  app.get("/api/teams/:teamId/campaign-summary-timeseries", async (req: Request, res: Response) => {
    const timeRange = req.query.timeRange as 'week' | 'month' | 'year' || 'week';

    // Calculate date ranges based on timeRange
    const now = new Date();
    let startDate: string;
    let endDate: string;

    if (timeRange === 'week') {
      // Get current week start (Monday) and end (Sunday)
      const currentDay = now.getDay();
      const mondayOffset = currentDay === 0 ? -6 : 1 - currentDay; // Handle Sunday as 0
      const monday = new Date(now);
      monday.setDate(now.getDate() + mondayOffset);
      monday.setHours(0, 0, 0, 0);

      const sunday = new Date(monday);
      sunday.setDate(monday.getDate() + 6);
      sunday.setHours(23, 59, 59, 999);

      startDate = monday.toISOString().split('T')[0]; // YYYY-MM-DD
      endDate = sunday.toISOString().split('T')[0];
    } else if (timeRange === 'month') {
      // Get current month start and end
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
      const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);

      startDate = monthStart.toISOString().split('T')[0];
      endDate = monthEnd.toISOString().split('T')[0];
    } else { // year
      // Get current year January 1st and December 31st
      const yearStart = new Date(now.getFullYear(), 0, 1);
      const yearEnd = new Date(now.getFullYear(), 11, 31);

      startDate = yearStart.toISOString().split('T')[0];
      endDate = yearEnd.toISOString().split('T')[0];
    }

    // Call the RPC function
    const campaignSummaryTimeseries = await storage.getCampaignSummaryTimeseries(
      req.params.teamId,
      startDate,
      endDate,
      timeRange
    );
    res.json(campaignSummaryTimeseries);
  });

  // RPC endpoint for campaign summary reports
  app.post("/api/rpc/get_campaign_summary", async (req: Request, res: Response) => {
    try {
      const { _campaignid, _mediaid, _teamid, _startdate, _enddate } = req.body;
      const summary = await storage.getCampaignSummary(_campaignid, _mediaid, _teamid, _startdate, _enddate);
      res.json(summary);
    } catch (error) {
      console.error('Error calling get_campaign_summary:', error);
      res.status(500).json({ error: 'Failed to fetch campaign summary' });
    }
  });

  // RPC endpoint for screen performance reports
  app.post("/api/rpc/get_screen_performance", async (req: Request, res: Response) => {
    try {
      const { p_team_id, p_reportstartdate, p_reportenddate } = req.body;
      const performance = await storage.getScreenPerformance(p_team_id, p_reportstartdate, p_reportenddate);
      res.json(performance);
    } catch (error) {
      console.error('Error calling get_screen_performance:', error);
      res.status(500).json({ error: 'Failed to fetch screen performance' });
    }
  });

  // Create invoice
  app.post("/api/invoices", async (req: Request, res: Response) => {
    try {
      console.log('Raw request body:', JSON.stringify(req.body, null, 2));
      console.log('invoiceDate type:', typeof req.body.invoiceDate);
      console.log('invoiceDate value:', req.body.invoiceDate);
      console.log('paidAt type:', typeof req.body.paidAt);
      console.log('paidAt value:', req.body.paidAt);

      const invoiceData = insertInvoiceSchema.parse(req.body);
      console.log('Parsed invoice data:', invoiceData); // Debug log
      const invoice = await storage.createInvoice(invoiceData);
      res.status(201).json(invoice);
    } catch (error) {
      console.error('Error creating invoice:', error); // Debug log
      res.status(400).json({ message: "Invalid invoice data", error });
    }
  });

  // Get screen subscriptions for a team
  app.get("/api/teams/:teamId/screen-subscriptions", async (req: Request, res: Response) => {
    const screenSubscriptions = await storage.getScreenSubscriptionsByTeamId(req.params.teamId);
    res.json(screenSubscriptions);
  });

  // Get available screens for subscription
  app.get("/api/teams/:teamId/available-screens", async (req: Request, res: Response) => {
    const availableScreens = await storage.getAvailableScreensForSubscription(req.params.teamId);
    res.json(availableScreens);
  });

  // Bulk update screen registrations
  app.post("/api/screen-registrations/bulk-update", async (req: Request, res: Response) => {
    try {
      const { updates } = req.body;

      if (!Array.isArray(updates)) {
        return res.status(400).json({ message: "Updates must be an array" });
      }

      // Validate each update object
      for (const update of updates) {
        if (!update.screenId || !update.trialEndsAt || !update.subscriptionStatus ||
            !update.billingCycle || !update.invoiceId) {
          return res.status(400).json({ message: "Invalid update data" });
        }
      }

      // Convert trialEndsAt strings to Date objects
      const processedUpdates = updates.map((update: any) => ({
        ...update,
        trialEndsAt: new Date(update.trialEndsAt),
      }));

      const success = await storage.bulkUpdateScreenRegistrations(processedUpdates);

      if (success) {
        res.json({ message: "Screen registrations updated successfully" });
      } else {
        res.status(500).json({ message: "Failed to update screen registrations" });
      }
    } catch (error) {
      res.status(400).json({ message: "Invalid request data", error });
    }
  });

  // Update invoice payment status
  app.patch("/api/invoices/:invoiceId/payment", async (req: Request, res: Response) => {
    try {
      const { paidAt } = req.body;
      // Convert ISO string to Date object if provided
      const paidAtDate = paidAt ? new Date(paidAt) : null;
      const updatedInvoice = await storage.updateInvoicePayment(req.params.invoiceId, paidAtDate);
      if (!updatedInvoice) {
        return res.status(404).json({ message: "Invoice not found" });
      }
      res.json(updatedInvoice);
    } catch (error) {
      res.status(400).json({ message: "Invalid payment data", error });
    }
  });

  // Delete invoice
  app.delete("/api/invoices/:invoiceId", async (req: Request, res: Response) => {
    const deleted = await storage.deleteInvoice(req.params.invoiceId);
    if (!deleted) {
      return res.status(404).json({ message: "Invoice not found" });
    }
    res.status(204).end();
  });

  const httpServer = createServer(app);

  return httpServer;
}
